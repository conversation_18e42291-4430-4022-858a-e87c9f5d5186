
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using System.Collections.Generic;
using System.Linq;

namespace Oxide.Plugins
{
    [Info("Password", "Wulf", "3.0.2")]
    [Description("Provides name and chat command password protection for the server")]
    class Password : CovalencePlugin
    {
        #region Configuration

        private Configuration config;

        class Configuration
        {
            [JsonProperty("Server password")]
            public string ServerPassword = "umod";

            [JsonProperty("Maximum password attempts")]
            public int MaxAttempts = 3;

            [JsonProperty("Always check for password on join")]
            public bool AlwaysCheck = true;

            public string ToJson() => JsonConvert.SerializeObject(this);

            public Dictionary<string, object> ToDictionary() => JsonConvert.DeserializeObject<Dictionary<string, object>>(ToJson());
        }

        protected override void LoadDefaultConfig() => config = new Configuration();

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    throw new JsonException();
                }

                if (!config.ToDictionary().Keys.SequenceEqual(Config.ToDictionary(x => x.Key, x => x.Value).Keys))
                {
                    LogWarning("Configuration appears to be outdated; updating and saving");
                    SaveConfig();
                }
            }
            catch
            {
                LogWarning($"Configuration file {Name}.json is invalid; using defaults");
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig()
        {
            LogWarning($"Configuration changes saved to {Name}.json");
            Config.WriteObject(config, true);
        }

        #endregion Configuration

        #region Localization

        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["CommandPassword"] = "password",
                ["NotAllowed"] = "You are not allowed to use the '{0}' command",
                ["PasswordAccepted"] = "Server password accepted, welcome!",
                ["PasswordChanged"] = "Server password has been changed to: {0}",
                ["PasswordCurrently"] = "Server password is currently set to: {0}",
                ["PasswordPrompt"] = "Please enter the server password with /{0} [password]",
                ["PasswordInvalid"] = "Invalid server password",
                ["MaximumAttempts"] = "You've exhausted the maximum password attempts ({0})",
                ["NotFastEnough"] = "You did not enter a password fast enough ({0} seconds)"
            }, this);
        }

        #endregion Localization

        #region Initialization

        private readonly HashSet<string> authorized = new HashSet<string>();
        private readonly Dictionary<string, int> attempts = new Dictionary<string, int>();
        private readonly Dictionary<string, Timer> passwordTimers = new Dictionary<string, Timer>();
        private readonly Dictionary<string, Timer> freezeTimers = new Dictionary<string, Timer>();

        private const string permBypass = "password.bypass";

        private void Init()
        {
            permission.RegisterPermission(permBypass, this);

            AddCovalenceCommand("server.password", nameof(CommandServerPassword));
            AddLocalizedCommand(nameof(CommandPassword));
        }

        private void OnServerInitialized()
        {
            foreach (IPlayer player in players.Connected)
            {
                authorized.Add(player.Id);
            }
        }

        #endregion Initialization

        #region Login Checking

        private object CanUserLogin(string playerName, string playerId)
        {
            // Always allow the initial connection - we'll handle password checking after they connect
            return null;
        }

        #endregion Login Checking

        #region Auth Handling

        private void OnUserConnected(IPlayer player)
        {
            // Check if player has bypass permission
            if (player.HasPermission(permBypass))
            {
                authorized.Add(player.Id);
                Interface.Oxide.CallHook("OnPasswordAccepted", player.Name, player.Id);
                return;
            }

            // Check if player was previously authorized and AlwaysCheck is false
            if (!config.AlwaysCheck && authorized.Contains(player.Id))
            {
                Interface.Oxide.CallHook("OnPasswordAccepted", player.Name, player.Id);
                return;
            }

            // Player needs to enter password
            Message(player, "PasswordPrompt", GetLang("CommandPassword", player.Id));

            // Start timer for password entry
            passwordTimers[player.Id] = timer.Once(60f, () =>
            {
                if (!authorized.Contains(player.Id))
                {
                    player.Kick(GetLang("NotFastEnough", player.Id, 60));
                }
            });

            // Freeze player until they enter password
            GenericPosition originalPosition = player.Position();
            freezeTimers[player.Id] = timer.Every(0.01f, () =>
            {
                if (!player.IsConnected || authorized.Contains(player.Id))
                {
                    freezeTimers[player.Id]?.Destroy();
                    freezeTimers.Remove(player.Id);
                }
                else
                {
                    player.Teleport(originalPosition);
                }
            });
        }

        private void OnUserDisconnected(IPlayer player)
        {
            if (!config.AlwaysCheck && authorized.Contains(player.Id))
            {
                authorized.Remove(player.Id);
            }

            // Clean up timers and attempts
            if (passwordTimers.ContainsKey(player.Id))
            {
                passwordTimers[player.Id]?.Destroy();
                passwordTimers.Remove(player.Id);
            }
            if (freezeTimers.ContainsKey(player.Id))
            {
                freezeTimers[player.Id]?.Destroy();
                freezeTimers.Remove(player.Id);
            }
            if (attempts.ContainsKey(player.Id))
            {
                attempts.Remove(player.Id);
            }
        }

        #endregion Auth Handling

        #region Chat Handling

        private object OnUserChat(IPlayer player, string message)
        {
            if (!authorized.Contains(player.Id))
            {
                Message(player, "PasswordPrompt", GetLang("CommandPassword", player.Id));
                return true; // Block the chat message
            }
            return null; // Allow the chat message
        }

        #endregion Chat Handling

        #region Commands

        private void CommandPassword(IPlayer player, string command, string[] args)
        {
            if (args.Length < 1)
            {
                Message(player, "PasswordPrompt", command);
                return;
            }

            if (args[0] != config.ServerPassword)
            {
                if (attempts.ContainsKey(player.Id) && attempts[player.Id] + 1 >= config.MaxAttempts)
                {
                    player.Kick(GetLang("MaximumAttempts", player.Id, config.MaxAttempts));
                    return;
                }

                Message(player, "PasswordInvalid");
                if (attempts.ContainsKey(player.Id))
                {
                    attempts[player.Id] += 1;
                }
                else
                {
                    attempts.Add(player.Id, 1);
                }
                return;
            }

            // Password correct - authorize player
            authorized.Add(player.Id);
            Message(player, "PasswordAccepted");
            Interface.Oxide.CallHook("OnPasswordAccepted", player.Name, player.Id);

            // Clean up timers and attempts
            if (passwordTimers.ContainsKey(player.Id))
            {
                passwordTimers[player.Id]?.Destroy();
                passwordTimers.Remove(player.Id);
            }
            if (freezeTimers.ContainsKey(player.Id))
            {
                freezeTimers[player.Id]?.Destroy();
                freezeTimers.Remove(player.Id);
            }
            if (attempts.ContainsKey(player.Id))
            {
                attempts.Remove(player.Id);
            }
        }

        private void CommandServerPassword(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin)
            {
                Message(player, "NotAllowed", command);
                return;
            }

            if (args.Length < 1)
            {
                Message(player, "PasswordCurrently", config.ServerPassword);
                return;
            }

            config.ServerPassword = args[0].Sanitize(); // TODO: Combine all args to allow spaces?
            Message(player, "PasswordChanged", config.ServerPassword);
            SaveConfig();
        }

        #endregion Commands

        #region Helpers

        private void AddLocalizedCommand(string command)
        {
            foreach (string language in lang.GetLanguages(this))
            {
                Dictionary<string, string> messages = lang.GetMessages(language, this);
                foreach (KeyValuePair<string, string> message in messages)
                {
                    if (message.Key.Equals(command))
                    {
                        if (!string.IsNullOrEmpty(message.Value))
                        {
                            AddCovalenceCommand(message.Value, command);
                        }
                    }
                }
            }
        }

        private string GetLang(string langKey, string playerId = null, params object[] args)
        {
            return string.Format(lang.GetMessage(langKey, this, playerId), args);
        }

        private void Message(IPlayer player, string textOrLang, params object[] args)
        {
            if (player.IsConnected)
            {
                string message = GetLang(textOrLang, player.Id, args);
                player.Reply(message != textOrLang ? message : textOrLang);
            }
        }

        #endregion Helpers
    }
}
