//#define DEBUG

using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using System.Collections.Generic;
using System.Linq;

namespace Oxide.Plugins
{
    [Info("Password", "Wulf", "3.0.2")]
    [Description("Provides name and chat command password protection for the server")]
    class Password : CovalencePlugin
    {
        #region Configuration

        private Configuration config;

        class Configuration
        {
            [JsonProperty("Server password")]
            public string ServerPassword = "umod";

            [JsonProperty("Maxium password attempts")]
            public int MaxAttempts = 3;

            [JsonProperty("Always check for password on join")]
            public bool AlwaysCheck = true;

            [JsonProperty("Check for password in names")]
            public bool CheckNames = true;

            public string ToJson() => JsonConvert.SerializeObject(this);

            public Dictionary<string, object> ToDictionary() => JsonConvert.DeserializeObject<Dictionary<string, object>>(ToJson());
        }

        protected override void LoadDefaultConfig() => config = new Configuration();

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    throw new JsonException();
                }

                if (!config.ToDictionary().Keys.SequenceEqual(Config.ToDictionary(x => x.Key, x => x.Value).Keys))
                {
                    LogWarning("Configuration appears to be outdated; updating and saving");
                    SaveConfig();
                }
            }
            catch
            {
                LogWarning($"Configuration file {Name}.json is invalid; using defaults");
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig()
        {
            LogWarning($"Configuration changes saved to {Name}.json");
            Config.WriteObject(config, true);
        }

        #endregion Configuration

        #region Localization

        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["CommandPassword"] = "password",
                ["NotAllowed"] = "You are not allowed to use the '{0}' command",
                ["PasswordAccepted"] = "Server password accepted, welcome!",
                ["PasswordChanged"] = "Server password has been changed to: {0}",
                ["PasswordCurrently"] = "Server password is currently set to: {0}",
                ["PasswordRequired"] = "This server requires a password. Please include the password in your player name or use the password command.",
                ["PasswordInNameFormat"] = "To join this server, include the password '{0}' in your player name."
            }, this);
        }

        #endregion Localization

        #region Initialization

        private readonly HashSet<string> authorized = new HashSet<string>();

        private const string permBypass = "password.bypass";

        private void Init()
        {
            permission.RegisterPermission(permBypass, this);

            AddCovalenceCommand("server.password", nameof(CommandServerPassword));
            AddLocalizedCommand(nameof(CommandPassword));

            // Unsubscribe from chat and connection events since we're blocking at login
            Unsubscribe(nameof(OnUserConnected));
            Unsubscribe(nameof(OnBetterChat));
            Unsubscribe(nameof(OnUserChat));
        }

        private void OnServerInitialized()
        {
            foreach (IPlayer player in players.Connected)
            {
                authorized.Add(player.Id);
            }
        }

        #endregion Initialization

        #region Login Checking

        private object CanUserLogin(string playerName, string playerId)
        {
#if DEBUG
            LogWarning($"{playerName} authorized? {authorized.Contains(playerId)}");
#endif

            // Check if player has bypass permission
            if (permission.UserHasPermission(playerId, permBypass))
            {
                if (!authorized.Contains(playerId))
                {
                    authorized.Add(playerId);
                }
                Interface.Oxide.CallHook("OnPasswordAccepted", playerName, playerId);
                return null; // Allow login
            }

            // Check if password is in player name
            if (config.CheckNames && playerName.Contains(config.ServerPassword))
            {
                if (!authorized.Contains(playerId))
                {
                    authorized.Add(playerId);
                }
                Interface.Oxide.CallHook("OnPasswordAccepted", playerName, playerId);
                return null; // Allow login
            }

            // Check if player was previously authorized and AlwaysCheck is false
            if (!config.AlwaysCheck && authorized.Contains(playerId))
            {
                Interface.Oxide.CallHook("OnPasswordAccepted", playerName, playerId);
                return null; // Allow login
            }

            // Block login and show password requirement message
            if (config.CheckNames)
            {
                return GetLang("PasswordInNameFormat", playerId, config.ServerPassword);
            }
            else
            {
                return GetLang("PasswordRequired", playerId);
            }
        }

        #endregion Login Checking

        #region Auth Handling

        private void OnUserDisconnected(IPlayer player)
        {
            if (!config.AlwaysCheck && authorized.Contains(player.Id))
            {
                authorized.Remove(player.Id);
            }
        }

        #endregion Auth Handling



        #region Commands

        private void CommandPassword(IPlayer player, string command, string[] args)
        {
            // This command is kept for compatibility but won't be used since
            // password is now required before joining the server
            Message(player, "PasswordAccepted");
        }

        private void CommandServerPassword(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin)
            {
                Message(player, "NotAllowed", command);
                return;
            }

            if (args.Length < 1)
            {
                Message(player, "PasswordCurrently", config.ServerPassword);
                return;
            }

            config.ServerPassword = args[0].Sanitize(); // TODO: Combine all args to allow spaces?
            Message(player, "PasswordChanged", config.ServerPassword);
            SaveConfig();
        }

        #endregion Commands

        #region Helpers

        private void AddLocalizedCommand(string command)
        {
            foreach (string language in lang.GetLanguages(this))
            {
                Dictionary<string, string> messages = lang.GetMessages(language, this);
                foreach (KeyValuePair<string, string> message in messages)
                {
                    if (message.Key.Equals(command))
                    {
                        if (!string.IsNullOrEmpty(message.Value))
                        {
                            AddCovalenceCommand(message.Value, command);
                        }
                    }
                }
            }
        }

        private string GetLang(string langKey, string playerId = null, params object[] args)
        {
            return string.Format(lang.GetMessage(langKey, this, playerId), args);
        }

        private void Message(IPlayer player, string textOrLang, params object[] args)
        {
            if (player.IsConnected)
            {
                string message = GetLang(textOrLang, player.Id, args);
                player.Reply(message != textOrLang ? message : textOrLang);
            }
        }

        #endregion Helpers
    }
}
